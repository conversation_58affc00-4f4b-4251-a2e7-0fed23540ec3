#!/usr/bin/env python
#coding=utf-8

'''
阿凡达2电影资源获取最终解决方案
提供多种方法获取阿凡达2电影资源
'''

import webbrowser
import os

def open_search_sites():
    """
    打开相关搜索网站
    """
    print("正在打开相关搜索网站...")
    
    sites = [
        {
            'name': '电影天堂',
            'url': 'http://www.dytt8.net',
            'search_url': 'http://www.dytt8.net/html/gndy/dyzz/index.html',
            'description': '搜索"阿凡达2"或"水之道"'
        },
        {
            'name': '磁力搜索',
            'url': 'https://www.btdig.com',
            'search_url': 'https://www.btdig.com/search?q=阿凡达2',
            'description': '磁力链接搜索'
        },
        {
            'name': '人人影视',
            'url': 'http://www.rrys2020.com',
            'search_url': 'http://www.rrys2020.com/search/阿凡达2',
            'description': '高质量字幕版本'
        }
    ]
    
    for site in sites:
        print(f"\n{site['name']}: {site['description']}")
        print(f"网址: {site['url']}")
        
        try:
            # 尝试打开搜索页面
            webbrowser.open(site['search_url'])
        except:
            print(f"无法自动打开 {site['name']}，请手动访问: {site['url']}")

def show_manual_search_guide():
    """
    显示手动搜索指南
    """
    print("\n" + "=" * 60)
    print("手动搜索指南")
    print("=" * 60)
    
    guide = """
1. 电影天堂搜索步骤:
   a) 访问 www.dytt8.net
   b) 在首页或最新电影页面搜索
   c) 使用关键词: "阿凡达2", "水之道", "Avatar 2"
   d) 查看2022年12月-2023年6月的电影列表
   
2. 磁力搜索步骤:
   a) 使用磁力搜索引擎 (如 btdig.com, torrentkitty.tv)
   b) 搜索关键词: "阿凡达2 1080p", "Avatar 2 2022"
   c) 选择种子数多、文件大小合理的资源
   d) 使用迅雷、qBittorrent等工具下载
   
3. 字幕组资源:
   a) 人人影视 (rrys2020.com)
   b) 字幕库 (zimuku.la)
   c) 射手网 (shooter.cn)
   
4. 流媒体平台:
   a) 爱奇艺、腾讯视频、优酷 (可能需要VIP)
   b) Netflix, Disney+ (海外平台)
   c) iTunes, Google Play (付费租赁/购买)
    """
    
    print(guide)

def show_download_links():
    """
    显示已知的下载资源信息
    """
    print("\n" + "=" * 60)
    print("已知资源信息 (仅供参考)")
    print("=" * 60)
    
    resources = [
        {
            'title': '阿凡达2：水之道 BD1080p中英双字',
            'size': '约8-12GB',
            'format': 'mkv/mp4',
            'quality': '蓝光1080p',
            'subtitle': '中英双字幕',
            'source': '电影天堂/人人影视'
        },
        {
            'title': '阿凡达2：水之道 4K UHD',
            'size': '约20-30GB', 
            'format': 'mkv',
            'quality': '4K超高清',
            'subtitle': '中英双字幕',
            'source': '蓝光原盘资源'
        },
        {
            'title': '阿凡达2：水之道 720p',
            'size': '约2-4GB',
            'format': 'mp4',
            'quality': '高清720p',
            'subtitle': '中英双字幕', 
            'source': '压缩版本'
        }
    ]
    
    for i, resource in enumerate(resources, 1):
        print(f"\n资源 {i}:")
        for key, value in resource.items():
            print(f"  {key}: {value}")

def create_search_script():
    """
    创建搜索脚本文件
    """
    script_content = '''
# 阿凡达2搜索关键词
keywords = [
    "阿凡达2",
    "阿凡达：水之道", 
    "Avatar 2",
    "Avatar: The Way of Water",
    "Avatar The Way of Water 2022",
    "阿凡达2 1080p",
    "阿凡达2 BD",
    "阿凡达2 中英双字"
]

# 推荐搜索网站
websites = [
    "www.dytt8.net",
    "www.rrys2020.com", 
    "btdig.com",
    "torrentkitty.tv",
    "rarbg.to",
    "1337x.to"
]

# 磁力链接格式示例
magnet_example = "magnet:?xt=urn:btih:[HASH]&dn=阿凡达2.水之道.Avatar.The.Way.of.Water.2022.1080p.BluRay.x264"

print("使用以上关键词在推荐网站搜索阿凡达2资源")
'''
    
    with open('avatar2_search_keywords.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"\n已创建搜索关键词文件: avatar2_search_keywords.py")

def main():
    """
    主函数
    """
    print("=" * 60)
    print("阿凡达2电影资源获取解决方案")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 显示电影信息")
        print("2. 打开搜索网站")
        print("3. 显示手动搜索指南")
        print("4. 显示已知资源信息")
        print("5. 创建搜索关键词文件")
        print("6. 退出")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == '1':
            # 导入并运行电影信息显示
            try:
                exec(open('avatar2_info.py', encoding='utf-8').read())
            except FileNotFoundError:
                print("avatar2_info.py 文件未找到")
                
        elif choice == '2':
            open_search_sites()
            
        elif choice == '3':
            show_manual_search_guide()
            
        elif choice == '4':
            show_download_links()
            
        elif choice == '5':
            create_search_script()
            
        elif choice == '6':
            print("感谢使用！请支持正版电影。")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == '__main__':
    main()
