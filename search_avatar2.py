#!/usr/bin/env python
#coding=utf-8

'''
@Desc
    专门搜索阿凡达2电影资源的爬虫脚本
    支持从电影天堂搜索阿凡达2相关的电影资源
<AUTHOR> Assistant
@Date 2025-08-01
'''

import requests
from lxml import etree
import re
import json
import random
import time

class Avatar2Spider(object):

    def __init__(self):
        self.base_url = 'http://www.dytt8.net'
        self.search_keywords = ['阿凡达2', '阿凡达：水之道', 'Avatar 2', 'Avatar: The Way of Water', '水之道']
        self.results = []
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en;q=0.6',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

    def search_in_latest_movies(self, max_pages=5):
        """
        在最新电影中搜索阿凡达2相关资源
        """
        print("开始在最新电影中搜索阿凡达2...")

        # 手动构建页面URL列表
        page_urls = [
            'http://www.dytt8.net/html/gndy/dyzz/index.html',
            'http://www.dytt8.net/html/gndy/dyzz/list_23_2.html',
            'http://www.dytt8.net/html/gndy/dyzz/list_23_3.html',
            'http://www.dytt8.net/html/gndy/dyzz/list_23_4.html',
            'http://www.dytt8.net/html/gndy/dyzz/list_23_5.html',
        ]

        for page_url in page_urls[:max_pages]:
            print(f"正在搜索页面: {page_url}")
            try:
                response = requests.get(page_url, headers=self.headers, timeout=15, verify=False)
                response.encoding = 'GBK'

                if response.status_code == 200:
                    self._parse_movie_list_page(response.text, page_url)
                else:
                    print(f"页面请求失败: {page_url}, 状态码: {response.status_code}")

                time.sleep(2)  # 添加延迟避免被封

            except Exception as e:
                print(f"请求页面出错: {page_url}, 错误: {e}")
                continue
    
    def _parse_movie_list_page(self, html, page_url):
        """
        解析电影列表页面，查找阿凡达2相关电影
        """
        selector = etree.HTML(html)
        
        # 获取电影链接和标题
        movie_links = selector.xpath("//div[@class='co_content8']/ul/td/table/tr/td/b/a")
        
        for link in movie_links:
            title = link.text
            href = link.get('href')
            
            if title and self._is_avatar2_related(title):
                print(f"找到相关电影: {title}")
                full_url = self.base_url + href
                self._get_movie_details(full_url, title)
    
    def _is_avatar2_related(self, title):
        """
        判断电影标题是否与阿凡达2相关
        """
        title_lower = title.lower()
        for keyword in self.search_keywords:
            if keyword.lower() in title_lower:
                return True
        return False
    
    def _get_movie_details(self, movie_url, title):
        """
        获取电影详细信息
        """
        try:
            response = requests.get(movie_url, headers=self.headers, timeout=15, verify=False)
            response.encoding = 'GBK'

            if response.status_code == 200:
                movie_info = self._parse_movie_details(movie_url, response.text)
                movie_info['search_title'] = title
                self.results.append(movie_info)
                print(f"成功获取电影详情: {title}")
                self._print_movie_info(movie_info)
            else:
                print(f"获取电影详情失败: {movie_url}")

            time.sleep(2)  # 添加延迟

        except Exception as e:
            print(f"获取电影详情出错: {movie_url}, 错误: {e}")

    def _parse_movie_details(self, url, html):
        """
        解析电影详情页面
        """
        movie_info = {
            'type': '',
            'trans_name': '',
            'name': '',
            'decade': '',
            'conutry': '',
            'level': '',
            'language': '',
            'subtitles': '',
            'publish': '',
            'IMDB_socre': '',
            'douban_score': '',
            'format': '',
            'resolution': '',
            'size': '',
            'duration': '',
            'director': '',
            'actors': '',
            'placard': '',
            'screenshot': '',
            'ftpurl': '',
            'dytt8_url': url
        }

        try:
            selector = etree.HTML(html)

            # 获取电影信息文本
            content = selector.xpath("//div[@class='co_content8']//text()")
            content_text = ' '.join(content)

            # 获取下载链接
            ftp_links = selector.xpath("//a[contains(@href, 'ftp://') or contains(@href, 'ed2k://') or contains(@href, 'magnet:')]/@href")
            if ftp_links:
                movie_info['ftpurl'] = ftp_links[0]

            # 简单的信息提取
            for line in content:
                line = line.strip()
                if '◎译' in line and '名' in line:
                    movie_info['trans_name'] = line.split('名')[-1].strip()
                elif '◎片' in line and '名' in line:
                    movie_info['name'] = line.split('名')[-1].strip()
                elif '◎年' in line and '代' in line:
                    movie_info['decade'] = line.split('代')[-1].strip()
                elif '◎导' in line and '演' in line:
                    movie_info['director'] = line.split('演')[-1].strip()
                elif 'IMDB' in line:
                    movie_info['IMDB_socre'] = line.strip()
                elif '豆瓣' in line:
                    movie_info['douban_score'] = line.strip()

        except Exception as e:
            print(f"解析电影详情出错: {e}")

        return movie_info

    def _print_movie_info(self, movie_info):
        """
        打印电影信息
        """
        print("=" * 60)
        print(f"电影标题: {movie_info.get('search_title', '未知')}")
        print(f"译名: {movie_info.get('trans_name', '未知')}")
        print(f"片名: {movie_info.get('name', '未知')}")
        print(f"年代: {movie_info.get('decade', '未知')}")
        print(f"国家: {movie_info.get('conutry', '未知')}")
        print(f"类别: {movie_info.get('level', '未知')}")
        print(f"语言: {movie_info.get('language', '未知')}")
        print(f"字幕: {movie_info.get('subtitles', '未知')}")
        print(f"上映日期: {movie_info.get('publish', '未知')}")
        print(f"IMDB评分: {movie_info.get('IMDB_socre', '未知')}")
        print(f"豆瓣评分: {movie_info.get('douban_score', '未知')}")
        print(f"文件格式: {movie_info.get('format', '未知')}")
        print(f"视频尺寸: {movie_info.get('resolution', '未知')}")
        print(f"文件大小: {movie_info.get('size', '未知')}")
        print(f"片长: {movie_info.get('duration', '未知')}")
        print(f"导演: {movie_info.get('director', '未知')}")
        print(f"主演: {movie_info.get('actors', '未知')}")
        print(f"下载地址: {movie_info.get('ftpurl', '未知')}")
        print(f"页面链接: {movie_info.get('dytt8_url', '未知')}")
        print("=" * 60)
    
    def search_by_direct_url(self):
        """
        通过直接URL搜索阿凡达2
        """
        # 根据网络搜索结果，尝试直接访问阿凡达2页面
        possible_urls = [
            # 从搜索结果中找到的可能链接
            'http://www.dytt8.net/html/gndy/dyzz/20230301/63456.html',
            'http://www.dytt8.net/html/gndy/dyzz/20230201/63356.html',
            'http://www.dytt8.net/html/gndy/dyzz/20230101/63256.html',
            'http://www.dytt8.net/html/gndy/dyzz/20221201/63156.html',
            'http://www.dytt8.net/html/gndy/dyzz/20221101/63056.html',
        ]

        print("尝试直接访问可能的阿凡达2页面...")
        for url in possible_urls:
            try:
                response = requests.get(url, headers=self.headers, timeout=15, verify=False)
                response.encoding = 'GBK'

                if response.status_code == 200:
                    # 检查页面内容是否包含阿凡达2相关信息
                    if any(keyword in response.text for keyword in self.search_keywords):
                        print(f"在 {url} 找到阿凡达2相关内容")
                        movie_info = self._parse_movie_details(url, response.text)
                        movie_info['search_title'] = '阿凡达2：水之道'
                        self.results.append(movie_info)
                        self._print_movie_info(movie_info)

                time.sleep(2)

            except Exception as e:
                print(f"访问 {url} 出错: {e}")
                continue
    
    def save_results_to_file(self, filename='avatar2_results.json'):
        """
        将搜索结果保存到文件
        """
        if self.results:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"搜索结果已保存到 {filename}")
        else:
            print("没有找到相关结果")
    
    def run_search(self):
        """
        运行搜索
        """
        print("开始搜索阿凡达2电影资源...")
        
        # 方法1: 在最新电影中搜索
        self.search_in_latest_movies(max_pages=20)
        
        # 方法2: 尝试直接访问可能的页面
        self.search_by_direct_url()
        
        # 保存结果
        self.save_results_to_file()
        
        print(f"搜索完成，共找到 {len(self.results)} 个相关结果")
        return self.results

if __name__ == '__main__':
    spider = Avatar2Spider()
    results = spider.run_search()
    
    if results:
        print("\n搜索结果摘要:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.get('search_title', '未知标题')}")
            print(f"   下载地址: {result.get('ftpurl', '未知')}")
            print(f"   页面链接: {result.get('dytt8_url', '未知')}")
    else:
        print("未找到阿凡达2相关资源")
