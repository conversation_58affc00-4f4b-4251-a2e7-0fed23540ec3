#!/usr/bin/env python
#coding=utf-8

'''
简单的阿凡达2搜索脚本
直接搜索电影天堂的阿凡达2资源
'''

import requests
from lxml import etree
import re
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def search_avatar2():
    """
    搜索阿凡达2电影资源
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en;q=0.6',
        'Connection': 'keep-alive',
    }
    
    # 搜索关键词
    keywords = ['阿凡达2', '阿凡达：水之道', 'Avatar 2', 'Avatar: The Way of Water', '水之道']
    
    print("开始搜索阿凡达2电影资源...")
    
    # 方法1: 搜索最新电影页面
    search_urls = [
        'http://www.dytt8.net/html/gndy/dyzz/index.html',
        'http://www.dytt8.net/html/gndy/dyzz/list_23_2.html',
        'http://www.dytt8.net/html/gndy/dyzz/list_23_3.html',
    ]
    
    found_movies = []
    
    for url in search_urls:
        print(f"\n正在搜索页面: {url}")
        try:
            response = requests.get(url, headers=headers, timeout=15, verify=False)
            response.encoding = 'GBK'
            
            if response.status_code == 200:
                # 解析页面
                selector = etree.HTML(response.text)
                
                # 查找电影链接
                movie_links = selector.xpath("//div[@class='co_content8']//a")
                
                for link in movie_links:
                    title = link.text
                    href = link.get('href')
                    
                    if title and href:
                        # 检查是否包含阿凡达2相关关键词
                        for keyword in keywords:
                            if keyword.lower() in title.lower():
                                print(f"找到相关电影: {title}")
                                full_url = f"http://www.dytt8.net{href}" if href.startswith('/') else href
                                found_movies.append({
                                    'title': title,
                                    'url': full_url
                                })
                                break
            else:
                print(f"页面请求失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"搜索页面出错: {e}")
            continue
    
    # 方法2: 直接尝试访问可能的阿凡达2页面
    print("\n尝试直接访问可能的阿凡达2页面...")
    
    # 基于时间推测的可能URL（阿凡达2于2022年12月上映）
    possible_urls = []
    base_url = "http://www.dytt8.net/html/gndy/dyzz/"
    
    # 生成2022年12月到2023年6月的可能URL
    for year in [2022, 2023]:
        for month in range(1, 13):
            if year == 2022 and month < 12:
                continue
            if year == 2023 and month > 6:
                break
            
            month_str = f"{month:02d}"
            for day in range(1, 32):
                day_str = f"{day:02d}"
                for id_num in range(63000, 64000, 50):  # 推测的ID范围
                    url = f"{base_url}{year}{month_str}{day_str}/{id_num}.html"
                    possible_urls.append(url)
    
    # 只测试前20个URL避免过多请求
    test_urls = possible_urls[:20]
    
    for url in test_urls:
        try:
            response = requests.get(url, headers=headers, timeout=10, verify=False)
            response.encoding = 'GBK'
            
            if response.status_code == 200:
                # 检查页面内容是否包含阿凡达2相关信息
                content = response.text
                for keyword in keywords:
                    if keyword in content:
                        print(f"在 {url} 找到阿凡达2相关内容")
                        found_movies.append({
                            'title': '阿凡达2：水之道',
                            'url': url
                        })
                        break
                        
        except Exception as e:
            # 忽略404等错误，继续搜索
            continue
    
    # 获取电影详情
    print(f"\n共找到 {len(found_movies)} 个相关结果")
    
    for i, movie in enumerate(found_movies, 1):
        print(f"\n=== 电影 {i}: {movie['title']} ===")
        print(f"页面链接: {movie['url']}")
        
        # 获取详细信息
        try:
            response = requests.get(movie['url'], headers=headers, timeout=15, verify=False)
            response.encoding = 'GBK'
            
            if response.status_code == 200:
                selector = etree.HTML(response.text)
                
                # 提取下载链接
                download_links = selector.xpath("//a[contains(@href, 'ftp://') or contains(@href, 'ed2k://') or contains(@href, 'magnet:')]/@href")
                
                if download_links:
                    print("下载链接:")
                    for link in download_links[:3]:  # 只显示前3个链接
                        print(f"  {link}")
                else:
                    print("未找到下载链接")
                
                # 提取基本信息
                content_text = selector.xpath("//div[@class='co_content8']//text()")
                content_str = ' '.join([text.strip() for text in content_text if text.strip()])
                
                # 查找关键信息
                if '◎译' in content_str:
                    trans_match = re.search(r'◎译.*?名.*?([^\n◎]+)', content_str)
                    if trans_match:
                        print(f"译名: {trans_match.group(1).strip()}")
                
                if '◎片' in content_str:
                    name_match = re.search(r'◎片.*?名.*?([^\n◎]+)', content_str)
                    if name_match:
                        print(f"片名: {name_match.group(1).strip()}")
                
                if '◎年' in content_str:
                    year_match = re.search(r'◎年.*?代.*?([^\n◎]+)', content_str)
                    if year_match:
                        print(f"年代: {year_match.group(1).strip()}")
                
                if '◎导' in content_str:
                    director_match = re.search(r'◎导.*?演.*?([^\n◎]+)', content_str)
                    if director_match:
                        print(f"导演: {director_match.group(1).strip()}")
                        
            else:
                print(f"获取详情失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"获取电影详情出错: {e}")
    
    if not found_movies:
        print("未找到阿凡达2相关资源，可能需要:")
        print("1. 检查网络连接")
        print("2. 尝试使用代理")
        print("3. 网站可能已更新，需要调整搜索策略")

if __name__ == '__main__':
    search_avatar2()
